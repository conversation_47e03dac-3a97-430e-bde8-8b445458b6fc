{"id": 10, "title": "Park Maintenance Mobile App", "organizationId": 1, "category": "Mobile Development", "tags": ["React Native", "Mobile", "iOS", "Android", "GPS"], "hourlyRateMin": 65, "hourlyRateMax": 85, "description": "Develop a mobile application for park maintenance staff to report issues, track maintenance tasks, and coordinate with central operations. The app should work offline and sync when connected.", "deliveryTimeWeeks": 8, "estimatedHours": 120, "status": "Unavailable", "toolsRequired": ["React Native", "TypeScript", "SQLite", "GPS APIs"], "notes": "Offline functionality is critical. App must work in areas with poor connectivity.", "postedDate": "2025-08-03"}